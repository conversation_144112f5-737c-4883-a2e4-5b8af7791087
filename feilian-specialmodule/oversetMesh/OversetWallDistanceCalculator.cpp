#include "feilian-specialmodule/oversetMesh/OversetWallDistanceCalculator.h"
#include "feilian-specialmodule/oversetMesh/OversetDonorSearcher.h"

namespace Overset
{

    OversetWallDistanceCalculator::OversetWallDistanceCalculator(Mesh *mesh_,
                                                                 ZoneManager *zoneManager_,
                                                                 const Configure::Flow::FlowConfigure &flowConfig_,
                                                                 const boost::mpi::communicator &mpi_world_)
        : localMesh(mesh_), zoneManager(zoneManager_), flowConfig(flowConfig_), mpi_world(mpi_world_),
          processorID(mpi_world_.rank()), nProcessor(mpi_world_.size()), wallDistanceManager(nullptr)
    {
        n_Zones = zoneManager->GetZoneNumber();
        dim = localMesh->GetMeshDim();
    }

    OversetWallDistanceCalculator::~OversetWallDistanceCalculator()
    {
        Clear();
    }

    void OversetWallDistanceCalculator::Initialize(Turbulence::WallDistance wallDistMethod_,
                                                   AssembleMethod elemTypeMethod_)
    {
        wallDistMethod = wallDistMethod_;
        elemTypeMethod = elemTypeMethod_;
        correctionMethod = WallDistCorrectionMethod::IMPROVED_NORMAL; // 默认使用改进法向量法

        // 清理之前的数据
        Clear();

        // 创建壁面距离管理器
        wallDistanceManager = new WallDistanceManager(wallDistMethod, false); // 不使用投影方式

        // 收集全局壁面数据
        CollectGlobalWallFaces();
    }

    void OversetWallDistanceCalculator::CalculateWallDistances(OversetDonorSearcher *donorSearcher)
    {
        if (wallDistanceManager == nullptr)
        {
            FatalError("OversetWallDistanceCalculator: 壁面距离管理器未初始化");
        }

        wallDistances.resize(n_Zones);
        nearestWallFaceID.resize(n_Zones);

        // 为每个子域计算壁面距离
        for (int zoneID = 0; zoneID < n_Zones; zoneID++)
        {
            if (globalWallFaceVectors[zoneID].empty())
            {
                // 子域没有壁面，设置为极大值
                wallDistances[zoneID].resize(localMesh->GetElementNumberReal(), INF);
                nearestWallFaceID[zoneID].resize(localMesh->GetElementNumberReal(), -1);
            }
            else
            {
                // 使用WallDistanceManager计算壁面距离
                wallDistanceManager->Calculate(localMesh, globalWallFaceVectors[zoneID],
                                               wallDistances[zoneID], nearestWallFaceID[zoneID]);
            }
        }

        // 修正壁面距离
        if (correctionMethod == WallDistCorrectionMethod::DONOR_SEARCH_BASED && donorSearcher != nullptr)
        {
            CorrectWallDist(donorSearcher);
        }
        else
        {
            CorrectWallDistImproved();
        }
    }

    void OversetWallDistanceCalculator::SetCorrectionMethod(WallDistCorrectionMethod method)
    {
        correctionMethod = method;
    }

    void OversetWallDistanceCalculator::UpdateWallDistField()
    {
        switch (elemTypeMethod)
        {
        case AssembleMethod::ElemBased:
        {
            Scalar minDist;
            for (int elemID = 0; elemID < localMesh->n_elemNum; elemID++)
            {
                minDist = INF;
                for (int zoneID = 0; zoneID < n_Zones; zoneID++)
                {
                    minDist = Min(minDist, wallDistances[zoneID][elemID]);
                }
                localMesh->v_nearWallDistance[elemID] = minDist;
            }
        }
        break;

        case AssembleMethod::NodeBased:
            for (int zoneID = 0; zoneID < n_Zones; zoneID++)
            {
                for (int elemID = 0; elemID < localMesh->n_elemNum; elemID++)
                {
                    Node elemCenter = localMesh->v_elem[elemID].GetCenter();
                    std::vector<Scalar> temp = {elemCenter.X(), elemCenter.Y(), elemCenter.Z()};
                    std::pair<Scalar, int> nearest = globalWallFaceKdtTrees[zoneID]->FindNearestNeighbour(temp);
                    if (nearest.first < localMesh->v_nearWallDistance[elemID])
                    {
                        localMesh->v_nearWallDistance[elemID] = nearest.first;
                    }
                }
            }
            break;

        default:
            break;
        }
    }

    Scalar OversetWallDistanceCalculator::GetWallDistance(int elemID, int zoneID) const
    {
        if (zoneID >= 0 && zoneID < wallDistances.size() &&
            elemID >= 0 && elemID < wallDistances[zoneID].size())
        {
            return wallDistances[zoneID][elemID];
        }
        return INF;
    }

    bool OversetWallDistanceCalculator::IsNearestWallDistToSelf(int elemID, int elemZoneID) const
    {
        for (int otherZoneID = 0; otherZoneID < n_Zones; otherZoneID++)
        {
            if (otherZoneID != elemZoneID &&
                wallDistances[elemZoneID][elemID] > wallDistances[otherZoneID][elemID])
            {
                return false;
            }
        }
        return true;
    }

    void OversetWallDistanceCalculator::Clear()
    {
        // 清理壁面距离管理器
        if (wallDistanceManager != nullptr)
        {
            delete wallDistanceManager;
            wallDistanceManager = nullptr;
        }

        // 清理数据容器
        globalWallFaceVectors.clear();
        wallDistances.clear();
        nearestWallFaceID.clear();
    }

    void OversetWallDistanceCalculator::CollectGlobalWallFaces()
    {
        if (wallDistanceManager == nullptr)
        {
            FatalError("OversetWallDistanceCalculator: 壁面距离管理器未初始化");
        }

        // 收集全局壁面边界面元列表
        globalWallFaceVectors.resize(n_Zones);

        // 为每个子域收集壁面面元
        for (int zoneID = 0; zoneID < n_Zones; zoneID++)
        {
            // 使用WallDistanceManager的CollectFace方法收集指定子域的壁面
            wallDistanceManager->CollectFace(localMesh, flowConfig, Boundary::Type::WALL,
                                             globalWallFaceVectors[zoneID], zoneID);
        }
    }

    void OversetWallDistanceCalculator::CorrectWallDist(OversetDonorSearcher *donorSearcher)
    {
        if (donorSearcher == nullptr)
        {
            return; // 没有贡献单元搜索器，跳过修正
        }

        List<List<int>> needDonorSearchElem;
        needDonorSearchElem.resize(n_Zones);

        // 第一步：识别需要修正的单元
        for (int elemID = 0; elemID < localMesh->GetElementNumberReal(); elemID++)
        {
            const int elemZoneID = zoneManager->GetElemZoneID(elemID);
            for (int zoneI = 0; zoneI < n_Zones; zoneI++)
            {
                const Vector &elemCenter = localMesh->GetElement(elemID).GetCenter();
                Scalar &wallDist = wallDistances[zoneI][elemID];

                if (zoneI < nearestWallFaceID.size() && elemID < nearestWallFaceID[zoneI].size())
                {
                    int &wallFaceID = nearestWallFaceID[zoneI][elemID];

                    if (wallFaceID >= 0 && wallFaceID < globalWallFaces[zoneI].size())
                    {
                        const Vector &wallFaceNormal = globalWallFaces[zoneI][wallFaceID].GetNormal();
                        const Vector &wallFaceCenter = globalWallFaces[zoneI][wallFaceID].GetCenter();
                        const auto &nodeList = globalWallFaceNodes[zoneI][wallFaceID];

                        Vector elemToFace = wallFaceCenter - elemCenter;
                        Vector unitWallFaceNormal = wallFaceNormal / wallFaceNormal.Mag();
                        Scalar normalDist = elemToFace & unitWallFaceNormal;

                        // 进行几何修正（简化版本）
                        if (normalDist < 0) // 需要进一步判断
                        {
                            if (elemZoneID == zoneI) // 是到自身子域的壁面，修正为正
                            {
                                wallDist = abs(wallDist);
                            }
                            else // 是到其他子域的壁面，需要通过贡献单元搜索判断
                            {
                                needDonorSearchElem[zoneI].push_back(elemID);
                            }
                        }
                    }
                }
            }
        }

        // 第二步：通过贡献单元搜索进行修正
        // 这部分需要与贡献单元搜索器协作，暂时简化实现
        // 实际实现中需要调用donorSearcher的相关方法
    }

    void OversetWallDistanceCalculator::CorrectWallDistImproved()
    {
        // 改进的壁面距离修正方法
        for (int elemID = 0; elemID < localMesh->GetElementNumberReal(); elemID++)
        {
            const int elemZoneID = zoneManager->GetElemZoneID(elemID);
            const Vector &elemCenter = localMesh->GetElement(elemID).GetCenter();

            for (int zoneI = 0; zoneI < n_Zones; zoneI++)
            {
                if (zoneI >= wallDistances.size() || elemID >= wallDistances[zoneI].size())
                    continue;

                Scalar &wallDist = wallDistances[zoneI][elemID];

                if (zoneI < nearestWallFaceID.size() && elemID < nearestWallFaceID[zoneI].size())
                {
                    int wallFaceID = nearestWallFaceID[zoneI][elemID];

                    if (wallFaceID >= 0 && wallFaceID < globalWallFaceVectors[zoneI].size())
                    {
                        const Face &wallFace = globalWallFaceVectors[zoneI][wallFaceID].first;
                        const auto &nodeList = globalWallFaceVectors[zoneI][wallFaceID].second;

                        // 使用改进的几何判断方法
                        bool isInside = IsPointInsideWallImproved(elemCenter, wallFace, nodeList, zoneI, elemZoneID);

                        if (isInside)
                        {
                            wallDist = -abs(wallDist); // 设为负值表示在壁面内部
                        }
                        else
                        {
                            wallDist = abs(wallDist); // 设为正值表示在壁面外部
                        }
                    }
                }
            }
        }
    }

    bool OversetWallDistanceCalculator::IsPointInsideWallImproved(const Vector &point,
                                                                  const Face &nearestFace,
                                                                  const std::vector<Node> &faceNodes,
                                                                  int zoneID,
                                                                  int elemZoneID)
    {
        switch (correctionMethod)
        {
        case WallDistCorrectionMethod::RAY_CASTING:
            return IsPointInsideWallRayCasting(point, zoneID);

        case WallDistCorrectionMethod::IMPROVED_NORMAL:
            return IsPointInsideWallImprovedNormal(point, nearestFace, faceNodes, elemZoneID == zoneID);

        case WallDistCorrectionMethod::TOPOLOGY_BASED:
            return IsPointInsideWallTopology(point, zoneID);

        case WallDistCorrectionMethod::HYBRID_METHOD:
            return IsPointInsideWallHybrid(point, nearestFace, faceNodes, zoneID, elemZoneID);

        default:
            return IsPointInsideWallImprovedNormal(point, nearestFace, faceNodes, elemZoneID == zoneID);
        }
    }

} // namespace Overset
