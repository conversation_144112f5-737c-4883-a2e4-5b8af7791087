////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file WallDistanceCorrectionMethods.cpp
//! <AUTHOR>
//! @brief 壁面距离修正方法的具体实现
//! @date 2024-12-20
//
//------------------------------修改日志----------------------------------------
// 2024-12-20 曾凯
//    说明：实现多种壁面距离修正方法，提高内外部判断精度
//------------------------------------------------------------------------------

#include "feilian-specialmodule/oversetMesh/OversetWallDistanceCalculator.h"
#include <cmath>
#include <algorithm>

namespace Overset
{
    bool OversetWallDistanceCalculator::IsPointInsideWallRayCasting(const Vector &point, int zoneID)
    {
        // 射线投射法实现
        if (zoneID >= globalWallFaceVectors.size() || globalWallFaceVectors[zoneID].empty())
            return false;

        // 定义多个射线方向以提高鲁棒性
        std::vector<Vector> rayDirections = {
            Vector(1.0, 0.0, 0.0),
            Vector(0.0, 1.0, 0.0),
            Vector(0.0, 0.0, 1.0),
            Vector(1.0, 1.0, 0.0).Normalize(),
            Vector(1.0, 0.0, 1.0).Normalize()
        };

        int insideVotes = 0;
        const Scalar tolerance = 1e-10;

        for (const auto &direction : rayDirections)
        {
            int intersectionCount = 0;
            
            // 遍历该子域的所有壁面面元
            for (const auto &wallFacePair : globalWallFaceVectors[zoneID])
            {
                const Face &face = wallFacePair.first;
                const auto &faceNodes = wallFacePair.second;
                
                if (faceNodes.size() < 3) continue; // 至少需要3个点构成面
                
                // 检查射线与面的交点
                Vector faceNormal = face.GetNormal().Normalize();
                Vector faceCenter = face.GetCenter();
                
                // 射线与面所在平面的交点
                Scalar denominator = direction & faceNormal;
                if (abs(denominator) < tolerance) continue; // 射线与面平行
                
                Scalar t = ((faceCenter - point) & faceNormal) / denominator;
                if (t <= tolerance) continue; // 交点在射线起点后方
                
                Vector intersectionPoint = point + t * direction;
                
                // 判断交点是否在面内部
                if (IsPointInPolygon(intersectionPoint, face, faceNodes))
                {
                    intersectionCount++;
                }
            }
            
            // 奇数个交点表示在内部
            if (intersectionCount % 2 == 1)
            {
                insideVotes++;
            }
        }
        
        // 多数投票决定
        return insideVotes > rayDirections.size() / 2;
    }

    bool OversetWallDistanceCalculator::IsPointInsideWallImprovedNormal(const Vector &point, 
                                                                        const Face &nearestFace, 
                                                                        const std::vector<Node> &faceNodes,
                                                                        bool isSameZone)
    {
        // 改进的法向量方法
        Vector faceNormal = nearestFace.GetNormal().Normalize();
        Vector faceCenter = nearestFace.GetCenter();
        Vector pointToFace = faceCenter - point;
        
        // 基础法向量判断
        Scalar normalDot = pointToFace & faceNormal;
        
        // 计算几何置信度
        Scalar confidence = CalculateGeometricConfidence(point, nearestFace, faceNodes);
        
        // 考虑局部曲率修正
        Scalar curvatureCorrection = CalculateLocalCurvatureCorrection(nearestFace, faceNodes);
        
        // 综合判断
        Scalar adjustedNormalDot = normalDot + curvatureCorrection;
        
        // 对于同一子域，更严格的判断标准
        Scalar threshold = isSameZone ? geometricTolerance * 0.1 : geometricTolerance;
        
        return (adjustedNormalDot > threshold) && (confidence > 0.7);
    }

    bool OversetWallDistanceCalculator::IsPointInsideWallTopology(const Vector &point, int zoneID)
    {
        // 拓扑连通性方法的简化实现
        // 这里需要更复杂的网格拓扑分析，暂时使用简化版本
        
        // 找到包含该点的网格单元
        int containingElem = FindContainingElement(point);
        if (containingElem < 0) return false;
        
        // 检查该单元是否被壁面包围
        return IsElementSurroundedByWall(containingElem, zoneID);
    }

    bool OversetWallDistanceCalculator::IsPointInsideWallHybrid(const Vector &point, 
                                                                const Face &nearestFace, 
                                                                const std::vector<Node> &faceNodes,
                                                                int zoneID, 
                                                                int elemZoneID)
    {
        // 混合方法：结合多种判断结果
        bool rayCastResult = IsPointInsideWallRayCasting(point, zoneID);
        bool normalResult = IsPointInsideWallImprovedNormal(point, nearestFace, faceNodes, elemZoneID == zoneID);
        bool topologyResult = IsPointInsideWallTopology(point, zoneID);
        
        // 加权投票（射线投射法权重最高）
        int voteCount = 0;
        if (rayCastResult) voteCount += 2;  // 权重2
        if (normalResult) voteCount += 1;   // 权重1
        if (topologyResult) voteCount += 1; // 权重1
        
        return voteCount >= 2; // 总权重>=2时判定为内部
    }

    // 辅助方法实现
    bool OversetWallDistanceCalculator::IsPointInPolygon(const Vector &point, 
                                                         const Face &face, 
                                                         const std::vector<Node> &faceNodes)
    {
        if (faceNodes.size() < 3) return false;
        
        Vector faceNormal = face.GetNormal().Normalize();
        
        // 使用叉积方法判断点是否在多边形内部
        bool isInside = true;
        for (size_t i = 0; i < faceNodes.size(); i++)
        {
            size_t next = (i + 1) % faceNodes.size();
            Vector edge = faceNodes[next] - faceNodes[i];
            Vector toPoint = point - faceNodes[i];
            Vector cross = edge ^ toPoint;
            
            // 检查叉积方向是否一致
            if ((cross & faceNormal) < 0)
            {
                isInside = false;
                break;
            }
        }
        
        return isInside;
    }

    Scalar OversetWallDistanceCalculator::CalculateGeometricConfidence(const Vector &point, 
                                                                       const Face &nearestFace, 
                                                                       const std::vector<Node> &faceNodes)
    {
        // 计算几何置信度
        Vector faceCenter = nearestFace.GetCenter();
        Scalar distToCenter = (point - faceCenter).Mag();
        Scalar faceSize = sqrt(nearestFace.GetArea());
        
        // 基于距离与面尺寸的比值计算置信度
        Scalar ratio = distToCenter / (faceSize + geometricTolerance);
        
        // 使用指数衰减函数
        return exp(-ratio);
    }

    Scalar OversetWallDistanceCalculator::CalculateLocalCurvatureCorrection(const Face &nearestFace, 
                                                                            const std::vector<Node> &faceNodes)
    {
        // 简化的局部曲率修正
        if (faceNodes.size() < 3) return 0.0;
        
        // 计算面的"弯曲程度"
        Vector faceNormal = nearestFace.GetNormal().Normalize();
        Scalar curvature = 0.0;
        
        // 通过相邻边的角度变化估算曲率
        for (size_t i = 0; i < faceNodes.size(); i++)
        {
            size_t prev = (i + faceNodes.size() - 1) % faceNodes.size();
            size_t next = (i + 1) % faceNodes.size();
            
            Vector edge1 = (faceNodes[i] - faceNodes[prev]).Normalize();
            Vector edge2 = (faceNodes[next] - faceNodes[i]).Normalize();
            
            Scalar angle = acos(std::max(-1.0, std::min(1.0, edge1 & edge2)));
            curvature += abs(M_PI - angle); // 偏离直线的程度
        }
        
        curvature /= faceNodes.size();
        
        // 返回基于曲率的修正值
        return curvature * geometricTolerance * 0.1;
    }

    int OversetWallDistanceCalculator::FindContainingElement(const Vector &point)
    {
        // 简化实现：找到包含指定点的网格单元
        // 实际实现中可以使用更高效的空间搜索算法
        
        for (int elemID = 0; elemID < localMesh->GetElementNumberReal(); elemID++)
        {
            const Element &elem = localMesh->GetElement(elemID);
            
            // 使用简单的包围盒检查
            Vector elemCenter = elem.GetCenter();
            Scalar elemSize = pow(elem.GetVolume(), 1.0/dim); // 估算单元特征尺寸
            
            if ((point - elemCenter).Mag() < elemSize)
            {
                // 进一步精确检查（这里简化为距离检查）
                return elemID;
            }
        }
        
        return -1; // 未找到
    }

    bool OversetWallDistanceCalculator::IsElementSurroundedByWall(int elemID, int zoneID)
    {
        // 简化实现：检查单元是否被壁面包围
        // 实际实现需要更复杂的拓扑分析
        
        const Element &elem = localMesh->GetElement(elemID);
        Vector elemCenter = elem.GetCenter();
        
        // 检查单元中心到壁面的最短距离
        if (zoneID < wallDistances.size() && elemID < wallDistances[zoneID].size())
        {
            Scalar wallDist = wallDistances[zoneID][elemID];
            Scalar elemSize = pow(elem.GetVolume(), 1.0/dim);
            
            // 如果壁面距离小于单元尺寸，可能被包围
            return wallDist < elemSize * 0.5;
        }
        
        return false;
    }

} // namespace Overset
