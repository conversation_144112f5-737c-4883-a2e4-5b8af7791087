////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file WallDistanceCorrector.h
//! <AUTHOR>
//! @brief 壁面距离修正器基类和具体实现
//! @date 2024-12-20
//
//------------------------------修改日志----------------------------------------
// 2024-12-20 曾凯
//    说明：新增壁面距离修正器，提供多种内外部判断方法，替代依赖DonorSearch的方法
//------------------------------------------------------------------------------

#ifndef _specialModule_oversetMesh_WallDistanceCorrector_
#define _specialModule_oversetMesh_WallDistanceCorrector_

#include "basic/mesh/Mesh.h"
#include "basic/dataStruct/List.h"
#include "feilian-specialmodule/oversetMesh/OverDefines.h"
#include "meshProcess/zone/ZoneManager.h"

namespace Overset
{
    /**
     * @brief 壁面距离修正器基类
     * 
     * 提供统一的接口用于判断点是否在壁面内部，并修正壁面距离的符号
     */
    class WallDistanceCorrector
    {
    public:
        /**
         * @brief 构造函数
         * 
         * @param mesh_ 网格指针
         * @param zoneManager_ 域管理器指针
         */
        WallDistanceCorrector(Mesh *mesh_, ZoneManager *zoneManager_);
        
        /**
         * @brief 虚析构函数
         */
        virtual ~WallDistanceCorrector() = default;
        
        /**
         * @brief 判断点是否在指定子域的壁面内部
         * 
         * @param point 目标点坐标
         * @param zoneID 子域编号
         * @param nearestFaceID 最近壁面面元ID
         * @return true 在壁面内部
         * @return false 在壁面外部
         */
        virtual bool IsPointInsideWall(const Node& point, int zoneID, int nearestFaceID) = 0;
        
        /**
         * @brief 修正壁面距离符号
         * 
         * @param wallDistances 壁面距离数组
         * @param nearestWallFaceID 最近壁面面元ID数组
         * @param globalWallFaces 全局壁面面元数据
         */
        void CorrectWallDistanceSigns(List<List<Scalar>>& wallDistances,
                                     const List<List<int>>& nearestWallFaceID,
                                     const List<List<Face>>& globalWallFaces);
        
        /**
         * @brief 设置几何容差
         * 
         * @param tolerance 几何容差值
         */
        void SetGeometricTolerance(Scalar tolerance) { geometricTolerance = tolerance; }
        
    protected:
        Mesh *localMesh;                    // 当前进程网格指针
        ZoneManager *zoneManager;           // 域管理器指针
        Scalar geometricTolerance;          // 几何容差
        int n_Zones;                        // 网格子域个数
        Mesh::MeshDim dim;                  // 网格维度
    };
    
    /**
     * @brief 射线投射法壁面距离修正器
     * 
     * 使用射线投射算法判断点是否在壁面内部
     */
    class RayCastingWallDistanceCorrector : public WallDistanceCorrector
    {
    public:
        RayCastingWallDistanceCorrector(Mesh *mesh_, ZoneManager *zoneManager_);
        
        bool IsPointInsideWall(const Node& point, int zoneID, int nearestFaceID) override;
        
        /**
         * @brief 设置射线方向数量
         * 
         * @param numRays 射线数量
         */
        void SetNumRays(int numRays) { numRayDirections = numRays; }
        
    private:
        /**
         * @brief 计算射线与壁面的交点数量
         * 
         * @param point 起始点
         * @param direction 射线方向
         * @param zoneID 子域编号
         * @return int 交点数量
         */
        int CountRayWallIntersections(const Node& point, const Vector& direction, int zoneID);
        
        /**
         * @brief 生成射线方向
         */
        void GenerateRayDirections();
        
        List<Vector> rayDirections;         // 射线方向列表
        int numRayDirections;               // 射线方向数量
    };
    
    /**
     * @brief 改进法向量壁面距离修正器
     * 
     * 使用改进的法向量方法判断点是否在壁面内部
     */
    class ImprovedNormalBasedCorrector : public WallDistanceCorrector
    {
    public:
        ImprovedNormalBasedCorrector(Mesh *mesh_, ZoneManager *zoneManager_);
        
        bool IsPointInsideWall(const Node& point, int zoneID, int nearestFaceID) override;
        
        /**
         * @brief 设置置信度阈值
         * 
         * @param threshold 置信度阈值
         */
        void SetConfidenceThreshold(Scalar threshold) { confidenceThreshold = threshold; }
        
    private:
        /**
         * @brief 计算局部曲率修正
         * 
         * @param faceID 面元ID
         * @param zoneID 子域编号
         * @return Scalar 曲率修正值
         */
        Scalar CalculateLocalCurvature(int faceID, int zoneID);
        
        /**
         * @brief 计算几何置信度
         * 
         * @param point 目标点
         * @param face 最近面元
         * @return Scalar 置信度值
         */
        Scalar CalculateGeometricConfidence(const Node& point, const Face& face);
        
        Scalar confidenceThreshold;        // 置信度阈值
    };
    
    /**
     * @brief 拓扑连通性壁面距离修正器
     * 
     * 使用网格拓扑连通性判断点是否在壁面内部
     */
    class TopologyBasedCorrector : public WallDistanceCorrector
    {
    public:
        TopologyBasedCorrector(Mesh *mesh_, ZoneManager *zoneManager_);
        
        bool IsPointInsideWall(const Node& point, int zoneID, int nearestFaceID) override;
        
        /**
         * @brief 初始化拓扑分析
         */
        void InitializeTopologyAnalysis();
        
    private:
        /**
         * @brief 标记外部区域
         * 
         * @param zoneID 子域编号
         */
        void MarkExteriorRegions(int zoneID);
        
        /**
         * @brief 获取单元邻居
         * 
         * @param elemID 单元ID
         * @return List<int> 邻居单元ID列表
         */
        List<int> GetElementNeighbors(int elemID);
        
        /**
         * @brief 判断两单元间是否被壁面阻挡
         * 
         * @param elem1 单元1
         * @param elem2 单元2
         * @return true 被阻挡
         * @return false 未被阻挡
         */
        bool IsWallBlocked(int elem1, int elem2);
        
        List<List<bool>> exteriorMarks;     // 外部区域标记
        bool topologyInitialized;          // 拓扑分析是否已初始化
    };
    
    /**
     * @brief 混合验证壁面距离修正器
     * 
     * 结合多种方法进行综合判断
     */
    class HybridWallDistanceCorrector : public WallDistanceCorrector
    {
    public:
        HybridWallDistanceCorrector(Mesh *mesh_, ZoneManager *zoneManager_);
        ~HybridWallDistanceCorrector();
        
        bool IsPointInsideWall(const Node& point, int zoneID, int nearestFaceID) override;
        
        /**
         * @brief 设置投票权重
         * 
         * @param rayCastWeight 射线投射权重
         * @param normalWeight 法向量权重
         * @param topologyWeight 拓扑权重
         */
        void SetVotingWeights(Scalar rayCastWeight, Scalar normalWeight, Scalar topologyWeight);
        
    private:
        RayCastingWallDistanceCorrector *rayCastingCorrector;
        ImprovedNormalBasedCorrector *normalCorrector;
        TopologyBasedCorrector *topologyCorrector;
        
        Scalar rayCastWeight;
        Scalar normalWeight;
        Scalar topologyWeight;
    };
}

#endif
