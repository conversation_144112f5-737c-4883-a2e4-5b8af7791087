﻿#include "feilian-specialmodule/oversetMesh/OversetMesh.h"

void OversetMesh::CalculateWallDistances()
{
	// 初始化壁面距离计算器
	wallDistanceCalculator->Initialize(wallDistMethod, elemTypeMethod);

	// 使用新的壁面距离计算器进行计算
	wallDistanceCalculator->CalculateWallDistances(donorSearcher);
}

void OversetMesh::CalculateWallDistByKDT()
{
	for (int zoneID = 0; zoneID < n_Zones; zoneID++)
	{
		// 子域网格中不存在壁面时，将所有网格对该子域的壁面距离设为极大值
		if (globalWallFaces[zoneID].size() == 0)
		{
			wallDistances[zoneID].resize(localMesh->GetElementNumberReal(), INF);
		}
		else
		{
			List<Scalar> temp(3, INF);
			Pair<Scalar, int> nearest;
			switch (elemTypeMethod)
			{
			case ElemTypeMethod::ElemBased:
				wallDistances[zoneID].resize(localMesh->GetElementNumberReal());
				nearestWallFaceID[zoneID].resize(localMesh->GetElementNumberReal());
				for (int elemID = 0; elemID < localMesh->GetElementNumberReal(); elemID++)
				{
					const Vector &node = localMesh->GetElement(elemID).GetCenter();
					temp[0] = node.X();
					temp[1] = node.Y();
					temp[2] = node.Z();
					nearest = globalWallFaceKdtTrees[zoneID]->FindNearestNeighbour(temp);
					wallDistances[zoneID][elemID] = nearest.first;
					nearestWallFaceID[zoneID][elemID] = nearest.second;
				}
				break;
			case ElemTypeMethod::NodeBased:
				wallDistances[zoneID].resize(localMesh->GetNodeNumber());
				nearestWallFaceID[zoneID].resize(localMesh->GetNodeNumber());
				for (int nodeID = 0; nodeID < localMesh->GetNodeNumber(); nodeID++)
				{
					const Vector &node = localMesh->GetNode(nodeID);
					temp = {node.X(), node.Y(), node.Z()};
					nearest = globalWallFaceKdtTrees[zoneID]->FindNearestNeighbour(temp);
					wallDistances[zoneID][nodeID] = nearest.first;
					nearestWallFaceID[zoneID][nodeID] = nearest.second;
					;
				}
				break;
			default:
				break;
			}
		}
	}
}

void OversetMesh::UpdateWallDistField()
{
	// 使用壁面距离计算器更新壁面距离场
	wallDistanceCalculator->UpdateWallDistField();
}

bool OversetMesh::IsNearestWallDistToSelf(int &elemID, int &elemZoneID)
{
	// 使用壁面距离计算器判断最近壁面距离是否来自自身子域
	return wallDistanceCalculator->IsNearestWallDistToSelf(elemID, elemZoneID);
}

void OversetMesh::CorrectWallDist()
{
	List<List<int>> needDonorSearchElem;
	needDonorSearchElem.resize(n_Zones);

	for (int elemID = 0; elemID < localMesh->GetElementNumberReal(); elemID++)
	{
		const int elemZoneID = zoneManager->GetElemZoneID(elemID);
		for (int zoneI = 0; zoneI < n_Zones; zoneI++)
		{
			const Vector &elemCenter = localMesh->GetElement(elemID).GetCenter();
			Scalar &wallDist = wallDistances[zoneI][elemID];
			int &wallFaceID = nearestWallFaceID[zoneI][elemID];
			const Vector &wallFaceNormal = globalWallFaces[zoneI][wallFaceID].GetNormal(); // 指向域外
			const Vector &wallFaceCenter = globalWallFaces[zoneI][wallFaceID].GetCenter();
			const auto &nodeList = globalWallFaceNodes[zoneI][wallFaceID];

			Vector elemToFace = wallFaceCenter - elemCenter;
			Vector unitWallFaceNormal = wallFaceNormal / wallFaceNormal.Mag();
			Scalar normalDist = elemToFace & unitWallFaceNormal;
			Vector normalDistVector = normalDist * unitWallFaceNormal;
			Vector projectNode = elemCenter + normalDistVector;

			// 对壁面距离进行修正
			{
				const int nodeNum = nodeList.size();

				if (nodeNum == 2) // 线段
				{
					const Node &node0 = nodeList[0];
					const Node &node1 = nodeList[1];

					if (((node0 - projectNode) & (node1 - projectNode)) < 0.0) // 投影点位于线段内
					{
						wallDist = normalDist;
					}
					else // 投影点位于线段外，返回距离两端点的最小值
					{
						const Vector distance0 = node0 - elemCenter;
						const Vector distance1 = node1 - elemCenter;
						wallDist = Min(distance0.Mag(), distance1.Mag());
					}
				}
				else // 多面形
				{
					// 计算投影点与各边构成的三角形的矢量面积
					List<Vector> area(nodeNum);
					for (int k = 0; k < nodeNum; k++)
					{
						const Node &node0 = nodeList[k];
						const Node &node1 = nodeList[(k + 1) % nodeNum];
						area[k] = (node1 - node0) ^ (projectNode - node1);
					}

					bool flag = true; // 投影点位于多边形内部
					for (int k = 0; k < nodeNum; k++)
					{
						if ((area[k] & area[(k + 1) % nodeNum]) < 0.0) // 投影点位于多边形外部
						{
							flag = false;
							break;
						}
					}

					if (flag)
						wallDist = normalDist; // 投影点位于多边形内部
					else					   // 投影点位于多边形外部
					{
						Scalar minDistance = INF;
						for (int k = 0; k < nodeNum; k++)
						{
							const Node &node0 = nodeList[k];
							const Scalar distance = (node0 - elemCenter).Mag();
							if (minDistance > distance)
								minDistance = distance;
						}
						wallDist = minDistance;
					}
				}
			}

			if (normalDist < 0) // 点到面心矢量与面法矢不同向时，可能存在一些特殊情况需要修正，如点实际在壁面外，但在最邻近壁面面元的内侧
			{
				if (elemZoneID == zoneI) // 是到自身子域的壁面，不可能为负，修正为正
				{
					wallDist = abs(wallDist);
				}
				else // 是到其他子域的壁面，需要通过贡献单元搜索判断该点是否确实落在物面内，添加到目标子域所属进程
				{
					needDonorSearchElem[zoneI].push_back(elemID);
				}
			}
		}
	}

	for (int zoneID = 0; zoneID < n_Zones; zoneID++) // 按域进行搜索，这样能知道返回搜索结果是针对哪个域的，TODO:修改贡献单元搜索算法，能兼容这种情况
	{
		List<List<Acceptor>> groupedAcpts;
		groupedAcpts.resize(nProcessor);
		const int &startRank = zoneManager->GetZoneStartRank(zoneID);
		const int &endRank = zoneManager->GetZoneEndRank(zoneID);

		for (int elemI = 0; elemI < needDonorSearchElem[zoneID].size(); elemI++)
		{
			const int &elemID = needDonorSearchElem[zoneID][elemI];
			const int &elemZoneID = zoneManager->GetElemZoneID(elemID);
			for (int procID = startRank; procID <= endRank; procID++)
			{
				if (this->ElemCenterInTree(elemID, globalTreeInfo[procID]))
				{
					const Node &elemCenter = this->localMesh->GetElement(elemID).GetCenter();
					Acceptor temp(elemID, processorID, elemZoneID, elemCenter);
					groupedAcpts[procID].push_back(temp);
				}
			}
		}

		// 对负壁面距离的单元进行贡献单元搜索，以判断是否确实在壁面内
		Set<Acceptor> searchResults;
		this->ParallelDonorSearch(groupedAcpts, searchResults);
		for (auto it = searchResults.begin(); it != searchResults.end(); it++)
		{
			const int &elemID = it->GetAcceptorID();
			const int &donorID = it->GetCentralDonorID();
			const Vector &elemCenter = localMesh->GetElement(elemID).GetCenter();
			if (donorID < 0) // 无贡献单元, 可能在壁面内也可能在重叠边界外
			{
				if (wallDistances[zoneID][elemID] > 0)
				{
					if (this->NodeInOversetBC(elemCenter)) // 没有贡献单元且在重叠边界内部，一定是壁面内单元
					{
						wallDistances[zoneID][elemID] *= -1;
					}
				}
			}
			else // 有贡献单元，不在壁面内
			{
				if (wallDistances[zoneID][elemID] < 0)
				{
					wallDistances[zoneID][elemID] *= -1;
				}
			}
		}
	}
}