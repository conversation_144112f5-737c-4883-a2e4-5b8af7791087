////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file OversetDonorSearcher.h
//! <AUTHOR>
//! @brief 重叠网格贡献单元搜索器
//! @date 2024-12-19
//
//------------------------------修改日志----------------------------------------
// 2024-12-19 曾凯
//    说明：从OversetMesh类中分离贡献单元搜索功能，提高代码模块化程度
//------------------------------------------------------------------------------

#ifndef _specialModule_oversetMesh_OversetDonorSearcher_
#define _specialModule_oversetMesh_OversetDonorSearcher_

#include "feilian-specialmodule/oversetMesh/Acceptor.h"
#include "feilian-specialmodule/oversetMesh/OverDefines.h"
#include "meshProcess/zone/ZoneManager.h"
#include "meshProcess/wallDistance/KDT_utilities.h"
#include "basic/mesh/Mesh.h"
#include "basic/dataStruct/List.h"
#include "basic/dataStruct/Set.h"
#include "feilian-external/linux-gcc7.3.0/boost/include/boost/mpi.hpp"
#include <chrono>
#include <memory>
#include <queue>
#include <unordered_map>

namespace Overset
{
    /**
     * @brief 重叠网格贡献单元搜索器
     *
     * 负责为插值单元搜索合适的贡献单元，支持KDT树加速和并行搜索
     */
    class DonorSearcher
    {
    public:
        /**
         * @brief 构造函数
         *
         * @param mesh_ 网格指针
         * @param zoneManager_ 域管理器指针
         * @param elemTypeField_ 单元类型场指针
         * @param mpi_world_ MPI通信器
         */
        DonorSearcher(Mesh *mesh_,
                      ZoneManager *zoneManager_,
                      ElementField<int> *elemTypeField_,
                      const boost::mpi::communicator &mpi_world_);

        /**
         * @brief 析构函数
         */
        ~DonorSearcher();

        /**
         * @brief 初始化贡献单元搜索器
         */
        void Initialize();

        /**
         * @brief 为批量插值单元并行搜索贡献单元
         *
         * @param groupedAcpts 按进程分组的插值单元列表
         * @param searchResults 搜索结果
         */
        void ParallelDonorSearch(List<List<Acceptor>> &groupedAcpts, Set<Acceptor> &searchResults);

        /**
         * @brief 为单个插值单元列表搜索贡献单元
         *
         * @param acptList 插值单元列表
         */
        void ChunkDonorSearch(List<Acceptor> &acptList);

        /**
         * @brief 为单个点搜索贡献单元（兼容性接口，已废弃）
         *
         * @param srcNode 插值点坐标
         * @param tgtTree 目标KDT树
         * @return int 贡献单元编号，未找到时返回-1
         */
        int DonorSearch(const Node &srcNode, KdtTree *tgtTree);

        /**
         * @brief 为单个点搜索贡献单元（使用KDT类）
         *
         * @param srcNode 插值点坐标
         * @param zoneID 目标子域编号
         * @return int 贡献单元编号，未找到时返回-1
         */
        int DonorSearchWithKDT(const Node &srcNode, int zoneID);

        /**
         * @brief 将插值单元按照可能存在贡献单元的进程分组
         *
         * @param searchElemID 需要搜索贡献单元的单元编号集合
         * @param groupedAcceptors 分组结果
         */
        void GroupingAcceptors(Set<int> &searchElemID, List<List<Acceptor>> &groupedAcceptors);

        /**
         * @brief 将插值单元按照贡献单元的进程号分组
         *
         * @param srcAcpts 源插值单元集合
         * @param groupedAcpts 分组结果
         */
        void GroupingAcceptors(const Set<Acceptor> &srcAcpts, List<List<Acceptor>> &groupedAcpts);

        /**
         * @brief 智能分组策略，考虑负载均衡
         *
         * @param searchElemID 需要搜索贡献单元的单元编号集合
         * @param groupedAcpts 分组结果
         */
        void SmartGroupingAcceptors(Set<int> &searchElemID, List<List<Acceptor>> &groupedAcpts);

        /**
         * @brief 判断指定单元是否可能与目标树相交
         *
         * @param elemID 单元编号
         * @param treeInfo 树信息
         * @return true 可能相交
         * @return false 不相交
         */
        bool ElemCenterInTree(const int &elemID, const TreeInfo &treeInfo);

        /**
         * @brief 获取全局树信息（只读访问）
         *
         * @return const std::vector<TreeInfo>& 全局树信息
         */
        const std::vector<TreeInfo> &GetGlobalTreeInfo() const;

        /**
         * @brief 获取贡献单元标记数组（只读访问）
         *
         * @return const List<int>& 贡献单元标记数组
         */
        const List<int> &GetAcceptorDonorFlag() const { return acceptorDonorFlag; }

        /**
         * @brief 清理搜索数据
         */
        void Clear();

        /**
         * @brief 性能指标结构
         */
        struct PerformanceMetrics
        {
            size_t totalSearches = 0;
            size_t successfulSearches = 0;
            size_t failedSearches = 0;
            double totalSearchTime = 0.0;
            double averageSearchTime = 0.0;
            double maxSearchTime = 0.0;
            std::unordered_map<int, size_t> zoneSearchCounts;
            std::unordered_map<int, size_t> zoneSuccessCounts;

            double getSuccessRate() const
            {
                return totalSearches > 0 ? (double)successfulSearches / totalSearches : 0.0;
            }

            void reset()
            {
                *this = PerformanceMetrics{};
            }
        };

        /**
         * @brief 获取性能指标
         */
        const PerformanceMetrics &getPerformanceMetrics() const { return performanceMetrics; }

        /**
         * @brief 重置性能指标
         */
        void resetPerformanceMetrics() { performanceMetrics.reset(); }

        // ==================== 动态网格支持接口 ====================

        /**
         * @brief 启用动态网格模式
         * @param rebuildThreshold 重建阈值（相对位移）
         * @param cacheValidityTime 缓存有效时间（秒）
         */
        void enableDynamicMesh(Scalar rebuildThreshold = 0.1, double cacheValidityTime = 1.0);

        /**
         * @brief 更新网格运动信息
         * @param displacementField 位移场
         * @param timeStep 时间步长
         * @param stepNumber 时间步编号
         */
        void updateMeshMotion(const std::vector<Vector> &displacementField,
                              double timeStep, int stepNumber);

        /**
         * @brief 增量更新KDT树
         * @param zoneID 子域编号
         * @return 是否成功增量更新（false表示需要完全重建）
         */
        bool incrementalUpdateKDT(int zoneID);

        /**
         * @brief 获取动态网格统计信息
         */
        struct DynamicMeshMetrics
        {
            size_t incrementalUpdates = 0;
            size_t fullRebuilds = 0;
            size_t cacheHits = 0;
            size_t cacheMisses = 0;
            size_t inheritanceHits = 0;
            double totalUpdateTime = 0.0;
            double averageDisplacement = 0.0;
            double maxDisplacement = 0.0;
        };
        const DynamicMeshMetrics &getDynamicMeshMetrics() const { return dynamicMetrics; }

    private:
        /**
         * @brief 合并各进程搜索结果，选择最优贡献单元
         *
         * @param groupedSearchResults 分组搜索结果
         * @param mergedResults 合并后的结果
         */
        void SelectBestDonor(const List<List<Acceptor>> &groupedSearchResults, List<Acceptor> &mergedResults);

        /**
         * @brief 判断点是否在网格单元内部
         *
         * @param node 坐标点
         * @param elem 网格单元
         * @return true 在内部
         * @return false 在外部
         */
        bool NodeInElem(const Node &node, const Element &elem);

        /**
         * @brief 创建指定子域的网格
         *
         * @param zoneID 子域编号
         * @return Mesh* 子域网格指针
         */
        Mesh *CreateZoneMesh(int zoneID);

        /**
         * @brief 计算网格特征长度
         *
         * @return Scalar 网格特征长度
         */
        Scalar calculateMeshCharacteristicLength();

    private:
        // 基础数据
        Mesh *localMesh;                           // 当前进程网格指针
        ZoneManager *zoneManager;                  // 域管理器指针
        ElementField<int> *elemTypeField;          // 网格单元重叠类型场
        const boost::mpi::communicator &mpi_world; // MPI通信器
        int n_Zones;                               // 网格子域个数
        Mesh::MeshDim dim;                         // 网格维度
        int processorID;                           // 当前进程ID
        int nProcessor;                            // 总进程数

        // KDT搜索器
        List<KDT *> kdtSearchers; // 各子域的KDT搜索器

        // 搜索状态
        List<int> acceptorDonorFlag; // 用于标记插值单元是否被用作了贡献单元

        // 性能监控
        mutable PerformanceMetrics performanceMetrics;

        // KDT搜索器池化管理
        class KDTSearcherPool
        {
        private:
            std::vector<std::unique_ptr<KDT>> pool;
            std::queue<KDT *> available;
            int dimension;

        public:
            KDTSearcherPool(int dim) : dimension(dim) {}

            KDT *acquire()
            {
                if (available.empty())
                {
                    pool.push_back(std::make_unique<KDT>(dimension));
                    return pool.back().get();
                }
                else
                {
                    KDT *searcher = available.front();
                    available.pop();
                    return searcher;
                }
            }

            void release(KDT *searcher)
            {
                if (searcher)
                {
                    available.push(searcher);
                }
            }

            void clear()
            {
                pool.clear();
                while (!available.empty())
                    available.pop();
            }
        };

        std::unique_ptr<KDTSearcherPool> kdtPool;

        // 自适应容差管理器
        class AdaptiveToleranceManager
        {
        private:
            Scalar baseTolerance = 1.0e-10;
            std::unordered_map<int, Scalar> elementTolerances;
            Scalar meshCharacteristicLength = 1.0;

        public:
            void setMeshCharacteristicLength(Scalar length)
            {
                meshCharacteristicLength = length;
                baseTolerance = length * 1.0e-6; // 相对容差
            }

            Scalar getToleranceForElement(const Element &elem)
            {
                int elemID = elem.GetID();
                auto it = elementTolerances.find(elemID);
                if (it != elementTolerances.end())
                {
                    return it->second;
                }

                // 基于单元尺寸计算容差
                Scalar elemSize = pow(elem.GetVolume(), 1.0 / 3.0); // 特征长度
                Scalar tolerance = std::max(baseTolerance, elemSize * 1.0e-6);
                elementTolerances[elemID] = tolerance;
                return tolerance;
            }

            void updateTolerance(int elemID, Scalar newTolerance)
            {
                elementTolerances[elemID] = newTolerance;
            }
        };

        std::unique_ptr<AdaptiveToleranceManager> toleranceManager;

        // ==================== 动态网格支持数据结构 ====================

        // 动态网格状态
        bool isDynamicMeshEnabled = false;
        Scalar rebuildThreshold = 0.1;
        double cacheValidityTime = 1.0;
        double currentTimeStep = 0.0;
        int currentStepNumber = 0;
        mutable DynamicMeshMetrics dynamicMetrics;

        // 网格运动信息
        std::vector<Vector> previousPositions;
        std::vector<Vector> currentDisplacements;
        std::vector<Scalar> elementDisplacements; // 每个单元的位移幅度

        // 增量式KDT更新管理器
        class IncrementalKDTManager
        {
        private:
            struct ZoneUpdateInfo
            {
                std::vector<int> modifiedElements;
                Scalar maxDisplacement = 0.0;
                std::chrono::time_point<std::chrono::steady_clock> lastUpdate;
                bool needsRebuild = false;
            };

            std::unordered_map<int, ZoneUpdateInfo> zoneUpdateInfo;
            Scalar rebuildThreshold;

        public:
            IncrementalKDTManager(Scalar threshold) : rebuildThreshold(threshold) {}

            bool shouldRebuild(int zoneID, Scalar maxDisplacement);
            void updateZoneInfo(int zoneID, const std::vector<int> &modifiedElems, Scalar maxDisp);
            void markForRebuild(int zoneID);
            const ZoneUpdateInfo &getZoneInfo(int zoneID) const;
        };

        std::unique_ptr<IncrementalKDTManager> kdtUpdateManager;

        // 搜索结果缓存管理器
        class SearchResultCache
        {
        private:
            struct CachedResult
            {
                Acceptor result;
                std::chrono::time_point<std::chrono::steady_clock> timestamp;
                Vector lastPosition;
                Scalar confidence = 1.0;
            };

            std::unordered_map<int, CachedResult> cache; // acceptorID -> CachedResult
            double validityTime;

        public:
            SearchResultCache(double validity) : validityTime(validity) {}

            bool tryGetCachedResult(int acceptorID, const Vector &currentPos, Acceptor &result);
            void cacheResult(const Acceptor &result, const Vector &position, Scalar confidence = 1.0);
            void invalidateByMotion(const std::vector<Vector> &displacements, Scalar threshold);
            void cleanupExpiredCache();
            size_t getCacheSize() const { return cache.size(); }
        };

        std::unique_ptr<SearchResultCache> resultCache;

        // 时间步继承管理器
        class TimeStepInheritanceManager
        {
        private:
            struct InheritanceInfo
            {
                std::unordered_map<int, Acceptor> previousResults;
                std::vector<Vector> previousPositions;
                double timeStep = 0.0;
                int stepNumber = 0;
            };

            InheritanceInfo previousStep;
            Scalar inheritanceThreshold = 0.8;

        public:
            bool predictFromPreviousStep(int acceptorID, const Vector &currentPos,
                                         const Vector &velocity, double dt, Acceptor &predicted);
            void saveCurrentStep(const std::unordered_map<int, Acceptor> &results,
                                 const std::vector<Vector> &positions, double dt, int step);
            bool validatePrediction(const Acceptor &predicted, const Acceptor &actual) const;
            void setInheritanceThreshold(Scalar threshold) { inheritanceThreshold = threshold; }
        };

        std::unique_ptr<TimeStepInheritanceManager> inheritanceManager;

        // 动态负载均衡器
        class DynamicLoadBalancer
        {
        private:
            struct ProcessorLoadInfo
            {
                size_t searchCount = 0;
                double searchTime = 0.0;
                size_t elementCount = 0;
                std::chrono::time_point<std::chrono::steady_clock> lastUpdate;
            };

            std::vector<ProcessorLoadInfo> processorLoads;
            double rebalanceThreshold = 0.3;

        public:
            DynamicLoadBalancer(int nProcs) : processorLoads(nProcs) {}

            void updateProcessorLoad(int procID, size_t searches, double time);
            bool needsRebalancing() const;
            int selectOptimalProcessor(const Vector &position, const std::vector<TreeInfo> &treeInfo) const;
            void resetLoadInfo();
        };

        std::unique_ptr<DynamicLoadBalancer> loadBalancer;

        // ==================== 动态网格辅助方法 ====================

        /**
         * @brief 分析网格运动模式
         */
        enum MotionType
        {
            RIGID_BODY,
            DEFORMATION,
            MIXED
        };
        MotionType analyzeMotionType(const std::vector<Vector> &displacements) const;

        /**
         * @brief 计算单元位移幅度
         */
        void calculateElementDisplacements(const std::vector<Vector> &displacements);

        /**
         * @brief 智能搜索策略（结合缓存和继承）
         */
        void smartDonorSearch(List<Acceptor> &acptList);

        /**
         * @brief 动态分组策略
         */
        void dynamicGroupingAcceptors(Set<int> &searchElemID, List<List<Acceptor>> &groupedAcpts);

        /**
         * @brief 执行实际的贡献单元搜索
         */
        void performActualSearch(Acceptor &acpt);

        /**
         * @brief 计算单元速度
         */
        Vector calculateVelocity(int elemID) const;
    };
}

#endif
