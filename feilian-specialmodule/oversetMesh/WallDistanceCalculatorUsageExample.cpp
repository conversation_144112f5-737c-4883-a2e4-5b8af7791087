////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file WallDistanceCalculatorUsageExample.cpp
//! <AUTHOR>
//! @brief OversetWallDistanceCalculator使用示例和性能对比
//! @date 2024-12-20
//
//------------------------------修改日志----------------------------------------
// 2024-12-20 曾凯
//    说明：展示改进后的OversetWallDistanceCalculator的使用方法和性能对比
//------------------------------------------------------------------------------

#include "feilian-specialmodule/oversetMesh/OversetWallDistanceCalculator.h"
#include <chrono>
#include <iostream>

namespace Overset
{
    /**
     * @brief 使用示例：展示如何使用改进的壁面距离计算器
     */
    void DemonstrateImprovedWallDistanceCalculator()
    {
        // 假设已有的网格和配置对象
        Mesh *mesh = nullptr;                    // 实际使用中需要有效的网格指针
        ZoneManager *zoneManager = nullptr;      // 实际使用中需要有效的域管理器
        Configure::Flow::FlowConfigure flowConfig; // 流场配置
        boost::mpi::communicator mpi_world;      // MPI通信器

        // 创建壁面距离计算器
        OversetWallDistanceCalculator calculator(mesh, zoneManager, flowConfig, mpi_world);

        // 初始化计算器
        calculator.Initialize(Turbulence::WallDistance::KDT, AssembleMethod::ElemBased);

        // 设置不同的修正方法进行对比测试
        std::vector<OversetWallDistanceCalculator::WallDistCorrectionMethod> methods = {
            OversetWallDistanceCalculator::WallDistCorrectionMethod::IMPROVED_NORMAL,
            OversetWallDistanceCalculator::WallDistCorrectionMethod::RAY_CASTING,
            OversetWallDistanceCalculator::WallDistCorrectionMethod::HYBRID_METHOD
        };

        std::vector<std::string> methodNames = {
            "改进法向量法",
            "射线投射法", 
            "混合验证法"
        };

        // 性能和精度对比测试
        for (size_t i = 0; i < methods.size(); i++)
        {
            std::cout << "\n=== 测试方法: " << methodNames[i] << " ===" << std::endl;
            
            // 设置修正方法
            calculator.SetCorrectionMethod(methods[i]);
            
            // 计时开始
            auto start = std::chrono::high_resolution_clock::now();
            
            // 执行壁面距离计算
            calculator.CalculateWallDistances();
            
            // 计时结束
            auto end = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
            
            std::cout << "计算时间: " << duration.count() << " ms" << std::endl;
            
            // 更新网格中的壁面距离场
            calculator.UpdateWallDistField();
            
            // 输出统计信息
            PrintWallDistanceStatistics(calculator, methodNames[i]);
        }
    }

    /**
     * @brief 输出壁面距离统计信息
     */
    void PrintWallDistanceStatistics(const OversetWallDistanceCalculator &calculator, 
                                     const std::string &methodName)
    {
        const auto &wallDistances = calculator.GetWallDistances();
        
        if (wallDistances.empty()) return;
        
        int totalElements = 0;
        int negativeDistCount = 0;
        Scalar minDist = INF, maxDist = -INF;
        Scalar avgDist = 0.0;
        
        // 统计所有子域的壁面距离
        for (size_t zoneID = 0; zoneID < wallDistances.size(); zoneID++)
        {
            for (size_t elemID = 0; elemID < wallDistances[zoneID].size(); elemID++)
            {
                Scalar dist = wallDistances[zoneID][elemID];
                
                if (dist < INF - 1) // 有效距离值
                {
                    totalElements++;
                    avgDist += abs(dist);
                    
                    if (dist < 0) negativeDistCount++;
                    
                    minDist = std::min(minDist, abs(dist));
                    maxDist = std::max(maxDist, abs(dist));
                }
            }
        }
        
        if (totalElements > 0)
        {
            avgDist /= totalElements;
            
            std::cout << "方法: " << methodName << std::endl;
            std::cout << "  总单元数: " << totalElements << std::endl;
            std::cout << "  负距离单元数: " << negativeDistCount 
                      << " (" << (100.0 * negativeDistCount / totalElements) << "%)" << std::endl;
            std::cout << "  最小距离: " << minDist << std::endl;
            std::cout << "  最大距离: " << maxDist << std::endl;
            std::cout << "  平均距离: " << avgDist << std::endl;
        }
    }

    /**
     * @brief 精度验证：对比不同方法的结果一致性
     */
    void ValidateMethodConsistency(OversetWallDistanceCalculator &calculator)
    {
        std::cout << "\n=== 方法一致性验证 ===" << std::endl;
        
        // 存储不同方法的结果
        std::vector<List<List<Scalar>>> methodResults;
        std::vector<OversetWallDistanceCalculator::WallDistCorrectionMethod> methods = {
            OversetWallDistanceCalculator::WallDistCorrectionMethod::IMPROVED_NORMAL,
            OversetWallDistanceCalculator::WallDistCorrectionMethod::RAY_CASTING,
            OversetWallDistanceCalculator::WallDistCorrectionMethod::HYBRID_METHOD
        };
        
        // 使用不同方法计算
        for (auto method : methods)
        {
            calculator.SetCorrectionMethod(method);
            calculator.CalculateWallDistances();
            methodResults.push_back(calculator.GetWallDistances());
        }
        
        // 比较结果一致性
        if (methodResults.size() >= 2)
        {
            int totalComparisons = 0;
            int consistentResults = 0;
            const Scalar tolerance = 1e-6;
            
            for (size_t zoneID = 0; zoneID < methodResults[0].size(); zoneID++)
            {
                for (size_t elemID = 0; elemID < methodResults[0][zoneID].size(); elemID++)
                {
                    bool allConsistent = true;
                    Scalar refValue = methodResults[0][zoneID][elemID];
                    
                    for (size_t methodID = 1; methodID < methodResults.size(); methodID++)
                    {
                        if (zoneID < methodResults[methodID].size() && 
                            elemID < methodResults[methodID][zoneID].size())
                        {
                            Scalar currentValue = methodResults[methodID][zoneID][elemID];
                            
                            // 检查符号一致性（内外部判断一致性）
                            if ((refValue > 0) != (currentValue > 0))
                            {
                                allConsistent = false;
                                break;
                            }
                            
                            // 检查数值一致性
                            if (abs(abs(refValue) - abs(currentValue)) > tolerance)
                            {
                                allConsistent = false;
                                break;
                            }
                        }
                    }
                    
                    totalComparisons++;
                    if (allConsistent) consistentResults++;
                }
            }
            
            Scalar consistencyRate = (100.0 * consistentResults) / totalComparisons;
            std::cout << "方法一致性: " << consistencyRate << "% (" 
                      << consistentResults << "/" << totalComparisons << ")" << std::endl;
            
            if (consistencyRate < 95.0)
            {
                std::cout << "警告: 方法一致性较低，建议检查参数设置或几何复杂度" << std::endl;
            }
        }
    }

    /**
     * @brief 性能基准测试
     */
    void BenchmarkPerformance(OversetWallDistanceCalculator &calculator, int iterations = 10)
    {
        std::cout << "\n=== 性能基准测试 (迭代次数: " << iterations << ") ===" << std::endl;
        
        std::vector<OversetWallDistanceCalculator::WallDistCorrectionMethod> methods = {
            OversetWallDistanceCalculator::WallDistCorrectionMethod::DONOR_SEARCH_BASED,
            OversetWallDistanceCalculator::WallDistCorrectionMethod::IMPROVED_NORMAL,
            OversetWallDistanceCalculator::WallDistCorrectionMethod::RAY_CASTING,
            OversetWallDistanceCalculator::WallDistCorrectionMethod::HYBRID_METHOD
        };
        
        std::vector<std::string> methodNames = {
            "传统DonorSearch法",
            "改进法向量法",
            "射线投射法",
            "混合验证法"
        };
        
        for (size_t i = 0; i < methods.size(); i++)
        {
            calculator.SetCorrectionMethod(methods[i]);
            
            auto totalStart = std::chrono::high_resolution_clock::now();
            
            for (int iter = 0; iter < iterations; iter++)
            {
                calculator.CalculateWallDistances();
            }
            
            auto totalEnd = std::chrono::high_resolution_clock::now();
            auto totalDuration = std::chrono::duration_cast<std::chrono::milliseconds>(totalEnd - totalStart);
            
            std::cout << methodNames[i] << ": " 
                      << "平均 " << (totalDuration.count() / iterations) << " ms/次, "
                      << "总计 " << totalDuration.count() << " ms" << std::endl;
        }
    }

    /**
     * @brief 内存使用分析
     */
    void AnalyzeMemoryUsage(const OversetWallDistanceCalculator &calculator)
    {
        std::cout << "\n=== 内存使用分析 ===" << std::endl;
        
        const auto &wallDistances = calculator.GetWallDistances();
        const auto &nearestWallFaceID = calculator.GetNearestWallFaceIDs();
        
        size_t totalElements = 0;
        size_t totalZones = wallDistances.size();
        
        // 计算存储的总元素数量
        for (const auto &zoneDistances : wallDistances)
        {
            totalElements += zoneDistances.size();
        }
        
        // 估算内存使用
        size_t scalarSize = sizeof(Scalar);
        size_t intSize = sizeof(int);
        
        size_t wallDistMemory = totalElements * scalarSize;
        size_t faceIDMemory = totalElements * intSize;
        size_t totalMemory = wallDistMemory + faceIDMemory;
        
        std::cout << "数据结构内存使用:" << std::endl;
        std::cout << "  子域数量: " << totalZones << std::endl;
        std::cout << "  总存储单元数: " << totalElements << std::endl;
        std::cout << "  壁面距离数组: " << (wallDistMemory / 1024.0 / 1024.0) << " MB" << std::endl;
        std::cout << "  面元ID数组: " << (faceIDMemory / 1024.0 / 1024.0) << " MB" << std::endl;
        std::cout << "  总内存使用: " << (totalMemory / 1024.0 / 1024.0) << " MB" << std::endl;
        
        // 内存效率分析
        if (totalZones > 0)
        {
            Scalar avgElementsPerZone = static_cast<Scalar>(totalElements) / totalZones;
            std::cout << "  平均每子域单元数: " << avgElementsPerZone << std::endl;
            
            if (avgElementsPerZone < 1000)
            {
                std::cout << "  建议: 考虑使用稀疏存储优化小规模子域" << std::endl;
            }
        }
    }

} // namespace Overset
